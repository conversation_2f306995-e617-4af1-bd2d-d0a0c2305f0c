import 'package:get/get.dart';
import 'package:rolio/common/interfaces/base_repository.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/constants/cache_constants.dart';
import 'package:rolio/env.dart';
import 'dart:async';

/// 搜索记录模型
class SearchRecord {
  final int id;
  final String keyword;
  final int searchCount;
  final DateTime lastSearchAt;
  
  SearchRecord({
    required this.id,
    required this.keyword,
    required this.searchCount,
    required this.lastSearchAt,
  });
  
  factory SearchRecord.fromJson(Map<String, dynamic> json) {
    return SearchRecord(
      id: json['id'] ?? 0,
      keyword: json['search_keyword'] ?? '',
      searchCount: json['search_count'] ?? 0,
      lastSearchAt: json['last_search_at'] != null 
        ? DateTime.parse(json['last_search_at']) 
        : DateTime.now(),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'search_keyword': keyword,
      'search_count': searchCount,
      'last_search_at': lastSearchAt.toIso8601String(),
    };
  }
}

/// 搜索仓库
/// 
/// 处理角色搜索相关的API请求
class SearchRepository extends BaseRepository {
  // 缓存管理器  
  // 构造函数
  SearchRepository();
  
  // 获取API基础URL
  String get _baseUrl => Env.envConfig.aiServiceUrl;
  
  /// 搜索角色
  /// 
  /// 根据关键词搜索角色
  /// [keyword] 搜索关键词
  /// [page] 页码
  /// [size] 每页大小
  /// [forceRefresh] 是否强制刷新，不使用缓存
  /// 返回搜索结果及分页信息
  Future<Map<String, dynamic>> searchRoles({
    required String keyword,
    int page = 1, 
    int size = 10,
    bool forceRefresh = false,
  }) async {
    try {
      LogUtil.debug('搜索角色，关键词: $keyword, 页码: $page, 大小: $size');
      
      // 构建请求体
      final requestBody = {
        'keyword': keyword,
        'page': page,
        'size': size,
      };
      
      // 发送请求
      final response = await HttpManager.post(
        url: '${_baseUrl}/search',
        body: requestBody,
      );
      
      
      // 解析响应
      final responseData = response.rawData;
      if (responseData is! Map<String, dynamic>) {
        LogUtil.warn('搜索角色响应数据结构异常，不是Map类型');
        throw ErrorHandler.createAppException(
          'Invalid search response format'
        );
      }
      
      // 检查数据字段
      if (!responseData.containsKey('data')) {
        LogUtil.warn('搜索角色响应数据结构异常，data字段不存在');
        throw ErrorHandler.createAppException(
          'Invalid search response structure, missing data field'
        );
      }
      
      final dynamic dataField = responseData['data'];
      if (dataField is! Map<String, dynamic>) {
        LogUtil.warn('搜索角色响应数据结构异常，data不是Map类型');
        throw ErrorHandler.createAppException(
          'Invalid search response data format'
        );
      }
      
      // 解析角色列表
      List<AiRole> roles = [];
      
      if (dataField.containsKey('items') && dataField['items'] is List) {
        final List<dynamic> items = dataField['items'];
        
        for (var item in items) {
          try {
            if (item is Map<String, dynamic>) {
              final role = AiRole.fromJson(item);
              
              // 验证必要字段
              if (role.id <= 0 || role.name.isEmpty) {
                LogUtil.warn('角色数据验证失败，跳过: $item');
                continue;
              }
              
              roles.add(role);
            }
          } catch (e) {
            LogUtil.error('解析角色数据失败: $e, 数据: $item');
          }
        }
      }
      
      // 提取分页信息
      final int total = dataField['total'] ?? 0;
      final int currentPage = dataField['page'] ?? page;
      final int pageSize = dataField['size'] ?? size;
      final int pages = dataField['pages'] ?? 0;
      
      LogUtil.debug('成功搜索角色: ${roles.length}个角色，总共: $total 个');
      
      // 准备结果
      final result = {
        'items': roles,
        'total': total,
        'page': currentPage,
        'size': pageSize,
        'pages': pages
      };
      
      return result;
    } catch (e) {
      LogUtil.error('搜索角色失败: $e');
      
      // 检查网络连接问题
      if (e.toString().toLowerCase().contains('timeout') || 
          e.toString().toLowerCase().contains('connection') ||
          e.toString().toLowerCase().contains('network')) {
        throw ErrorHandler.createAppException(
          e,
          'Network connection error while searching roles, please check your network'
        );
      }
      
      // 如果已经是AppException，直接抛出
      if (e is AppException) {
        throw e;
      }
      
      // 其他错误统一处理
      throw ErrorHandler.createAppException(
        e, 
        'Failed to search roles, please try again later'
      );
    }
  }
  
  /// 获取搜索建议
  ///
  /// 根据输入关键词返回匹配的角色名称建议列表
  /// [keyword] 关键词
  /// [limit] 返回数量限制
  /// 返回建议列表
  Future<List<String>> getSearchSuggestions(String keyword, {int limit = 10}) async {
    try {
      if (keyword.isEmpty) {
        return [];
      }
      
      // 生成缓存键
      final cacheKey = '${CacheConstants.searchSuggestionsCachePrefix}${_normalizeKeyword(keyword)}';
      
      // 先检查缓存
      final cacheManager = Get.find<CacheManager>();
      final cachedSuggestions = await cacheManager.get<List<String>>(
        cacheKey,
        strategy: CacheStrategy.memoryOnly,
        maxAge: CacheConstants.getExpiryForModule('search', type: 'suggestions'),
        fromJson: (json) => (json['suggestions'] as List<dynamic>).cast<String>(),
      );
      
      if (cachedSuggestions != null && cachedSuggestions.isNotEmpty) {
        LogUtil.debug('SearchRepository: 搜索建议缓存命中 - $keyword, 建议数量=${cachedSuggestions.length}');
        return cachedSuggestions;
      }
      
      // 构建请求参数
      final params = {
        'keyword': keyword,
        'limit': limit.toString(),
      };
      
      // 发送请求
      final response = await HttpManager.get(
        url: '${_baseUrl}/search/match',
        params: params,
      );
      
      if (response == null || response.rawData == null) {
        LogUtil.warn('获取搜索建议失败，响应为空');
        return [];
      }
      
      // 解析响应
      final responseData = response.rawData;
      if (responseData is! Map<String, dynamic> || 
          !responseData.containsKey('data') ||
          responseData['data'] is! Map<String, dynamic> ||
          !responseData['data'].containsKey('suggestions')) {
        LogUtil.warn('获取搜索建议响应数据结构异常');
        return [];
      }
      
      final suggestions = responseData['data']['suggestions'];
      if (suggestions is! List) {
        LogUtil.warn('搜索建议数据不是列表类型');
        return [];
      }
      
      // 转换为字符串列表
      final List<String> suggestionList = List<String>.from(suggestions.map((item) => item.toString()));
      
      // 只有当搜索建议不为空时才缓存
      if (suggestionList.isNotEmpty) {
        await cacheManager.set(
          cacheKey,
          {'suggestions': suggestionList},
          strategy: CacheStrategy.memoryOnly,
          expiry: CacheConstants.getExpiryForModule('search', type: 'suggestions'),
          toJson: (data) => data,
        );
        LogUtil.debug('SearchRepository: 缓存搜索建议 - $keyword, 建议数量=${suggestionList.length}');
      } else {
        LogUtil.debug('SearchRepository: 搜索建议为空，不进行缓存 - $keyword');
      }
      
      return suggestionList;
    } catch (e) {
      LogUtil.error('获取搜索建议失败: $e');
      return [];
    }
  }
  
  /// 标准化关键词（去除空格、转小写、去重复字符）
  String _normalizeKeyword(String keyword) {
    return keyword.trim().toLowerCase().replaceAll(RegExp(r'\s+'), '_');
  }
  
  /// 清理搜索建议缓存
  Future<void> clearSearchSuggestionsCache() async {
    try {
      final cacheManager = Get.find<CacheManager>();
      final keys = await cacheManager.getKeys(strategy: CacheStrategy.memoryOnly);
      
      int cleanedCount = 0;
      for (final key in keys) {
        if (key.startsWith(CacheConstants.searchSuggestionsCachePrefix)) {
          await cacheManager.remove(key, strategy: CacheStrategy.memoryOnly);
          cleanedCount++;
        }
      }
      
      if (cleanedCount > 0) {
        LogUtil.debug('SearchRepository: 清理了${cleanedCount}个搜索建议缓存');
      }
    } catch (e) {
      LogUtil.error('SearchRepository: 清理搜索建议缓存失败 - $e');
    }
  }
  
  /// 获取随机角色名
  ///
  /// 用于搜索框的默认提示
  /// 返回随机角色名
  Future<String> getRandomRoleName() async {
    try {
      // 发送请求
      final response = await HttpManager.get(
        url: '${_baseUrl}/search/random-name',
      );
      
      if (response == null || response.rawData == null) {
        LogUtil.warn('获取随机角色名失败，响应为空');
        return '';
      }
      
      // 解析响应
      final responseData = response.rawData;
      if (responseData is! Map<String, dynamic> || 
          !responseData.containsKey('data') ||
          responseData['data'] is! Map<String, dynamic> ||
          !responseData['data'].containsKey('role_name')) {
        LogUtil.warn('获取随机角色名响应数据结构异常');
        return '';
      }
      
      final roleName = responseData['data']['role_name']?.toString() ?? '';
      
      LogUtil.debug('成功获取随机角色名: $roleName');
      return roleName;
    } catch (e) {
      LogUtil.error('获取随机角色名失败: $e');
      return '';
    }
  }
  
  /// 获取搜索历史
  ///
  /// 返回用户的搜索历史记录
  /// [page] 页码
  /// [size] 每页大小
  /// [forceRefresh] 是否强制刷新
  /// 返回搜索历史及分页信息
  Future<Map<String, dynamic>> getSearchHistory({
    int page = 1,
    int size = 10,
    bool forceRefresh = false,
  }) async {
    try {
      // 构建请求参数
      final params = {
        'page': page.toString(),
        'size': size.toString(),
      };
      
      // 发送请求
      final response = await HttpManager.get(
        url: '${_baseUrl}/search/history',
        params: params,
      );
      
      if (response == null || response.rawData == null) {
        LogUtil.warn('获取搜索历史失败，响应为空');
        return _createEmptyHistoryResult(page, size);
      }
      
      // 解析响应
      final responseData = response.rawData;
      if (responseData is! Map<String, dynamic> || !responseData.containsKey('data')) {
        LogUtil.warn('获取搜索历史响应数据结构异常');
        return _createEmptyHistoryResult(page, size);
      }
      
      final dataField = responseData['data'];
      if (dataField is! Map<String, dynamic>) {
        LogUtil.warn('搜索历史数据不是Map类型');
        return _createEmptyHistoryResult(page, size);
      }
      
      // 解析搜索记录
      List<SearchRecord> records = [];
      
      if (dataField.containsKey('items') && dataField['items'] is List) {
        final List<dynamic> items = dataField['items'];
        
        for (var item in items) {
          try {
            if (item is Map<String, dynamic>) {
              final record = SearchRecord.fromJson(item);
              records.add(record);
            }
          } catch (e) {
            LogUtil.error('解析搜索历史记录失败: $e, 数据: $item');
          }
        }
      }
      
      // 提取分页信息
      final int total = dataField['total'] ?? 0;
      final int currentPage = dataField['page'] ?? page;
      final int pageSize = dataField['size'] ?? size;
      final int pages = dataField['pages'] ?? 0;
      
      // 准备结果
      final result = {
        'items': records,
        'total': total,
        'page': currentPage,
        'size': pageSize,
        'pages': pages
      };
      
      LogUtil.debug('成功获取搜索历史: ${records.length}条记录，总共: $total 条');
      return result;
    } catch (e) {
      LogUtil.error('获取搜索历史失败: $e');
      return _createEmptyHistoryResult(page, size);
    }
  }
  
  /// 删除搜索记录
  ///
  /// 删除指定的搜索记录
  /// [recordId] 记录ID
  /// 返回是否成功
  Future<bool> deleteSearchRecord(int recordId) async {
    try {
      LogUtil.debug('删除搜索记录，ID: $recordId');
      
      // 发送请求
      final response = await HttpManager.put(
        url: '${_baseUrl}/search/history/$recordId',
      );
      
      if (response == null || response.rawData == null) {
        LogUtil.warn('删除搜索记录失败，响应为空');
        return false;
      }
      
      // 解析响应
      final responseData = response.rawData;
      if (responseData is! Map<String, dynamic> || !responseData.containsKey('data')) {
        LogUtil.warn('删除搜索记录响应数据结构异常');
        return false;
      }
      
      final bool success = responseData['data'] == true;
      
      if (success) {
        LogUtil.debug('成功删除搜索记录: $recordId');
      } else {
        LogUtil.warn('删除搜索记录失败: $recordId');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('删除搜索记录失败: $e');
      return false;
    }
  }
  
  /// 删除所有搜索记录
  ///
  /// 清空用户的所有搜索历史
  /// 返回删除的数量和消息
  Future<Map<String, dynamic>> deleteAllSearchRecords() async {
    try {
      LogUtil.debug('开始删除所有搜索记录');
      
      // 发送请求
      final response = await HttpManager.put(
        url: '${_baseUrl}/search/history/batch',
      );
      
      if (response == null || response.rawData == null) {
        LogUtil.warn('删除所有搜索记录失败，响应为空');
        return {
          'deleted_count': 0,
          'message': '操作失败'
        };
      }
      
      // 解析响应
      final responseData = response.rawData;
      if (responseData is! Map<String, dynamic> || 
          !responseData.containsKey('data') || 
          responseData['data'] is! Map<String, dynamic>) {
        LogUtil.warn('删除所有搜索记录响应数据结构异常');
        return {
          'deleted_count': 0,
          'message': '响应数据异常'
        };
      }
      
      final dataField = responseData['data'];
      final int deletedCount = dataField['deleted_count'] ?? 0;
      final String message = dataField['message'] ?? '';
      
      if (deletedCount > 0) {
        LogUtil.debug('成功删除所有搜索记录: $deletedCount 条');
      } else {
        LogUtil.warn('删除所有搜索记录失败: $message');
      }
      
      return {
        'deleted_count': deletedCount,
        'message': message
      };
    } catch (e) {
      LogUtil.error('删除所有搜索记录失败: $e');
      return {
        'deleted_count': 0,
        'message': e.toString()
      };
    }
  }
  
  /// 创建空的搜索结果
  Map<String, dynamic> _createEmptySearchResult(int page, int size) {
    return {
      'items': <AiRole>[],
      'total': 0,
      'page': page,
      'size': size,
      'pages': 0
    };
  }
  
  /// 创建空的搜索历史结果
  Map<String, dynamic> _createEmptyHistoryResult(int page, int size) {
    return {
      'items': <SearchRecord>[],
      'total': 0,
      'page': page,
      'size': size,
      'pages': 0
    };
  }
}